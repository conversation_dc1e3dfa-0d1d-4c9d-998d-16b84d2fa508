from flask import Flask, render_template, request, jsonify
import re
import jieba

app = Flask(__name__)

class ChineseSentimentAnalyzer:
    def __init__(self):
        # 正面词汇
        self.positive_words = {
            '好', '棒', '优秀', '喜欢', '爱', '开心', '高兴', '满意', '赞', '完美',
            '不错', '很棒', '厉害', '优质', '推荐', '值得', '惊喜', '感谢', '支持',
            '美好', '舒服', '方便', '快速', '有效', '成功', '顺利', '幸福', '温暖'
        }
        
        # 负面词汇
        self.negative_words = {
            '差', '坏', '糟糕', '讨厌', '恨', '难过', '失望', '愤怒', '垃圾', '烂',
            '不好', '很差', '问题', '错误', '失败', '困难', '麻烦', '痛苦', '后悔',
            '无聊', '浪费', '不满', '抱怨', '批评', '反对', '拒绝', '担心', '害怕'
        }
        
        # 程度副词权重
        self.degree_words = {
            '非常': 2.0, '很': 1.5, '特别': 2.0, '极其': 2.5, '超级': 2.0,
            '比较': 1.2, '还算': 1.1, '稍微': 0.8, '有点': 0.9, '略微': 0.7
        }
        
        # 否定词
        self.negation_words = {'不', '没', '无', '非', '未', '别', '莫', '勿'}
    
    def analyze_sentiment(self, text):
        if not text.strip():
            return "中性"
        
        # 分词
        words = list(jieba.cut(text))
        
        positive_score = 0
        negative_score = 0
        
        i = 0
        while i < len(words):
            word = words[i]
            
            # 检查程度副词
            degree = 1.0
            if word in self.degree_words:
                degree = self.degree_words[word]
                i += 1
                if i >= len(words):
                    break
                word = words[i]
            
            # 检查否定词
            negation = False
            if word in self.negation_words:
                negation = True
                i += 1
                if i >= len(words):
                    break
                word = words[i]
            
            # 计算情感分数
            if word in self.positive_words:
                score = 1 * degree
                if negation:
                    negative_score += score
                else:
                    positive_score += score
            elif word in self.negative_words:
                score = 1 * degree
                if negation:
                    positive_score += score
                else:
                    negative_score += score
            
            i += 1
        
        # 判断结果
        if positive_score > negative_score:
            return "正面"
        elif negative_score > positive_score:
            return "负面"
        else:
            return "中性"

analyzer = ChineseSentimentAnalyzer()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/analyze', methods=['POST'])
def analyze():
    data = request.get_json()
    text = data.get('text', '')
    
    if not text.strip():
        return jsonify({'error': '请输入文本内容'})
    
    result = analyzer.analyze_sentiment(text)
    return jsonify({'sentiment': result})

if __name__ == '__main__':
    app.run(debug=True)